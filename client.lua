local QBCore = exports['qb-core']:GetCoreObject()
local PlayerData = QBCore.Functions.GetPlayerData()
local cashAmount = 0
local bankAmount = 0



RegisterNetEvent("QBCore:Client:OnPlayerUnload", function()
    PlayerData = {}
end)

RegisterNetEvent("QBCore:Player:SetPlayerData", function(val)
    PlayerData = val
end)


-- Money HUD
RegisterNetEvent('hud:client:ShowAccounts', function(type, amount)
    if type == 'bank' then
        SendNUIMessage({
            action = 'show',
            type = 'bank',
            bank = amount
        })
    end
end)

RegisterNetEvent('hud:client:OnMoneyChange', function(type, amount, isMinus)
    QBCore.Functions.GetPlayerData(function(PlayerData)
        bankAmount = PlayerData.money['bank']
    end)
    SendNUIMessage({
        action = 'update',
        bank = bankAmount,
        amount = amount,
        minus = isMinus,
        plus = not isMinus,
        type = type
    })
end)

-- Show bank constantly
CreateThread(function()
    while true do
        Wait(1000)
        if PlayerData and PlayerData.money then
            SendNUIMessage({
                action = 'showconstant',
                bank = PlayerData.money['bank']
            })
        end
    end
end)