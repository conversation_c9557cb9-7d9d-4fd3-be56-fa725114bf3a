<html>

<head>
    <meta id="viewport" name="viewport"
        content="width=device-width, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <link rel="stylesheet" type="text/css" href="styles.css" />
    <link rel="stylesheet" type="text/css" href="responsive.css" />
    <link href="https://cdn.jsdelivr.net/npm/quasar@2.1.0/dist/quasar.prod.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="https://pro.fontawesome.com/releases/v5.13.0/css/all.css">
    <script src="https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.prod.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/quasar@2.1.0/dist/quasar.umd.prod.js" defer></script>
    <script src="app.js" defer></script>
</head>

<body>
    <div id="main-container">
        <div id="money-container">
            <div id="money-bank">
                <transition name="slide-fade">
                    <div v-if="showBank" class="bank-display">
                        <div class="bank-icon">
                            <i class="fas fa-university"></i>
                        </div>
                        <div class="bank-amount">
                            <span id="bank" class="currency-text">{{(bank)}}</span>
                        </div>
                    </div>
                </transition>
            </div>
            <div id="money-change" v-if="showUpdate">
                <div class="change-amount">
                    <p v-if="plus" id="money"><span id="plus" class="currency-text">+&nbsp;</span><span id="money" class="currency-text">{{(amount)}}</span></p>
                    <p v-else-if="minus" id="minus"><span id="minus" class="currency-text">-&nbsp;</span><span id="money" class="currency-text">{{(amount)}}</span></p>
                </div>
            </div>
        </div>
    </div>
</body>

</html>