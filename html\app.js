// MONEY HUD
const moneyHud = Vue.createApp({
    data() {
        return {
            bank: 0,
            amount: 0,
            plus: false,
            minus: false,
            showBank: false,
            showUpdate: false,
            updateTimeout: null,
            bankTimeout: null,
            showTimeout: null,
        };
    },
    destroyed() {
        window.removeEventListener("message", this.listener);
        // Clear all timeouts when component is destroyed
        if (this.updateTimeout) clearTimeout(this.updateTimeout);
        if (this.bankTimeout) clearTimeout(this.bankTimeout);
        if (this.showTimeout) clearTimeout(this.showTimeout);
    },
    mounted() {
        this.listener = window.addEventListener("message", (event) => {
            switch (event.data.action) {
                case "showconstant":
                    this.showConstant(event.data);
                    break;
                case "update":
                    this.update(event.data);
                    break;
                case "show":
                    this.showAccounts(event.data);
                    break;
                case "hide":
                    this.hideAll();
                    break;
            }
        });
    },
    methods: {
        // CONFIGURE YOUR CURRENCY HERE
        // https://www.w3schools.com/tags/ref_language_codes.asp LANGUAGE CODES
        // https://www.w3schools.com/tags/ref_country_codes.asp COUNTRY CODES
        formatMoney(value) {
            const formatter = new Intl.NumberFormat("en-US", {
                style: "currency",
                currency: "USD",
                minimumFractionDigits: 0,
            });
            return formatter.format(value);
        },
        showConstant(data) {
            // Clear any existing timeouts
            if (this.bankTimeout) clearTimeout(this.bankTimeout);
            if (this.showTimeout) clearTimeout(this.showTimeout);

            this.showBank = true;
            this.bank = data.bank;
        },
        update(data) {
            // Clear any existing timeouts to prevent conflicts
            if (this.updateTimeout) clearTimeout(this.updateTimeout);
            if (this.bankTimeout) clearTimeout(this.bankTimeout);

            this.showUpdate = true;
            this.amount = data.amount;
            this.bank = data.bank;
            this.minus = data.minus;
            this.plus = data.plus;

            if (data.type === "bank") {
                this.showBank = true;

                // Hide the update display after 1.5 seconds
                this.updateTimeout = setTimeout(() => {
                    this.showUpdate = false;
                    this.plus = false;
                    this.minus = false;
                }, 1500);

                // Hide the bank display after 3 seconds
                this.bankTimeout = setTimeout(() => {
                    this.showBank = false;
                }, 3000);
            }
        },
        showAccounts(data) {
            if (data.type === "bank" && !this.showBank) {
                // Clear any existing timeout
                if (this.showTimeout) clearTimeout(this.showTimeout);

                this.showBank = true;
                this.bank = data.bank;

                this.showTimeout = setTimeout(() => {
                    this.showBank = false;
                }, 3500);
            }
        },
        // Method to force hide all displays (useful for debugging)
        hideAll() {
            if (this.updateTimeout) clearTimeout(this.updateTimeout);
            if (this.bankTimeout) clearTimeout(this.bankTimeout);
            if (this.showTimeout) clearTimeout(this.showTimeout);

            this.showBank = false;
            this.showUpdate = false;
            this.plus = false;
            this.minus = false;
        },
    },
}).mount("#money-container");